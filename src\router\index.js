import Vue from 'vue'
import VueRouter from 'vue-router'
// import HomeView from "../views/HomeView.vue";

import index from '../views/Index/index.vue'
import Login from '../views/login/index.vue'

Vue.use(VueRouter)

const routes = [
    {
        path: '/',
        name: 'Login',
        component: Login
    },
    {
        path: '/Index',
        name: 'Index',
        component: index,
        children: [
            {
                path: '/Home',
                name: 'Home',
                component: import('../views/Index/Home/HomeView.vue')
            }
        ]
    }
]

const router = new VueRouter({
    mode: 'history',
    base: process.env.BASE_URL,
    routes
})

export default router
