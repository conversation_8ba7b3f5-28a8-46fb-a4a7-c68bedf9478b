import Vue from 'vue'
import VueRouter from 'vue-router'

import index from '../views/Index/index.vue'
import Login from '../views/login/index.vue'

Vue.use(VueRouter)

const routes = [
    {
        path: '/',
        name: 'Login',
        component: Login
    },
    {
        path: '/index',
        name: 'Index',
        component: index,
        redirect: '/home',
        children: [
            {
                path: '/home',
                name: 'Home',
                component: () => import('../views/Index/Home/index.vue')
            },
            {
                path: '/courses',
                name: 'Courses',
                component: () => import('../views/Index/Courses/index.vue')
            },
            {
                path: '/study',
                name: 'Study',
                component: () => import('../views/Index/Study/index.vue')
            },
            {
                path: '/profile',
                name: 'Profile',
                component: () => import('../views/Index/Profile/index.vue')
            }
        ]
    }
]

const router = new VueRouter({
    mode: 'history',
    base: process.env.BASE_URL,
    routes
})

export default router
