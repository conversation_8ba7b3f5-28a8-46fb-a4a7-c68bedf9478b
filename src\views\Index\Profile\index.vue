<template>
    <div class="profile-page">
        <van-nav-bar title="个人中心" fixed placeholder />

        <div class="content">
            <!-- 用户信息 -->
            <div class="user-info">
                <div class="user-header">
                    <van-image
                        round
                        width="60"
                        height="60"
                        :src="userInfo.avatar"
                        @click="editAvatar"
                    />
                    <div class="user-details">
                        <div class="username">{{ userInfo.name }}</div>
                        <div class="user-level">{{ userInfo.level }}</div>
                        <div class="user-id">ID: {{ userInfo.id }}</div>
                    </div>
                    <van-icon name="edit" @click="editProfile" />
                </div>

                <!-- 学习数据 -->
                <div class="user-stats">
                    <van-grid :column-num="4" :border="false">
                        <van-grid-item>
                            <div class="stat-item">
                                <div class="stat-number">{{ userStats.courses }}</div>
                                <div class="stat-label">课程</div>
                            </div>
                        </van-grid-item>
                        <van-grid-item>
                            <div class="stat-item">
                                <div class="stat-number">{{ userStats.certificates }}</div>
                                <div class="stat-label">证书</div>
                            </div>
                        </van-grid-item>
                        <van-grid-item>
                            <div class="stat-item">
                                <div class="stat-number">{{ userStats.points }}</div>
                                <div class="stat-label">积分</div>
                            </div>
                        </van-grid-item>
                        <van-grid-item>
                            <div class="stat-item">
                                <div class="stat-number">{{ userStats.days }}</div>
                                <div class="stat-label">学习天数</div>
                            </div>
                        </van-grid-item>
                    </van-grid>
                </div>
            </div>

            <!-- 功能菜单 -->
            <div class="menu-section">
                <van-cell-group>
                    <van-cell
                        v-for="menu in menuList"
                        :key="menu.id"
                        :title="menu.title"
                        :icon="menu.icon"
                        :value="menu.value"
                        is-link
                        @click="handleMenuClick(menu)"
                    />
                </van-cell-group>
            </div>

            <!-- 设置菜单 -->
            <div class="settings-section">
                <van-cell-group>
                    <van-cell
                        v-for="setting in settingsList"
                        :key="setting.id"
                        :title="setting.title"
                        :icon="setting.icon"
                        is-link
                        @click="handleSettingClick(setting)"
                    />
                </van-cell-group>
            </div>

            <!-- 退出登录 -->
            <div class="logout-section">
                <van-button type="danger" block @click="logout"> 退出登录 </van-button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ProfilePage',
    data() {
        return {
            userInfo: {
                id: '123456',
                name: '张医生',
                level: '高级学员',
                avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
            },
            userStats: {
                courses: 12,
                certificates: 3,
                points: 2580,
                days: 45
            },
            menuList: [
                {
                    id: 1,
                    title: '我的课程',
                    icon: 'play-circle-o',
                    value: '',
                    path: '/my-courses'
                },
                {
                    id: 2,
                    title: '我的证书',
                    icon: 'certificate',
                    value: '',
                    path: '/my-certificates'
                },
                {
                    id: 3,
                    title: '学习记录',
                    icon: 'records',
                    value: '',
                    path: '/study-records'
                },
                {
                    id: 4,
                    title: '收藏夹',
                    icon: 'star-o',
                    value: '',
                    path: '/favorites'
                },
                {
                    id: 5,
                    title: '积分商城',
                    icon: 'gift-o',
                    value: '2580积分',
                    path: '/points-mall'
                }
            ],
            settingsList: [
                {
                    id: 1,
                    title: '账号设置',
                    icon: 'setting-o',
                    path: '/account-settings'
                },
                {
                    id: 2,
                    title: '消息通知',
                    icon: 'bell',
                    path: '/notifications'
                },
                {
                    id: 3,
                    title: '隐私设置',
                    icon: 'lock',
                    path: '/privacy-settings'
                },
                {
                    id: 4,
                    title: '帮助中心',
                    icon: 'question-o',
                    path: '/help'
                },
                {
                    id: 5,
                    title: '关于我们',
                    icon: 'info-o',
                    path: '/about'
                }
            ]
        }
    },
    methods: {
        editAvatar() {
            console.log('编辑头像')
            // 实现头像编辑功能
        },
        editProfile() {
            console.log('编辑个人信息')
            this.$router.push('/edit-profile')
        },
        handleMenuClick(menu) {
            console.log('点击菜单:', menu.title)
            this.$router.push(menu.path)
        },
        handleSettingClick(setting) {
            console.log('点击设置:', setting.title)
            this.$router.push(setting.path)
        },
        logout() {
            this.$dialog
                .confirm({
                    title: '确认退出',
                    message: '确定要退出登录吗？'
                })
                .then(() => {
                    // 清除用户信息
                    localStorage.removeItem('token')
                    localStorage.removeItem('userInfo')
                    // 跳转到登录页
                    this.$router.replace('/')
                })
                .catch(() => {
                    // 取消退出
                })
        }
    }
}
</script>

<style lang="scss" scoped>
// 蓝白主题色彩变量
$primary-blue: #2563eb;
$light-blue: #3b82f6;
$lighter-blue: #60a5fa;
$lightest-blue: #dbeafe;
$background-blue: #f8fafc;
$white: #ffffff;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-light: #94a3b8;

.profile-page {
    background: linear-gradient(180deg, $background-blue 0%, #ffffff 100%);
    min-height: 100vh;
    overflow-x: hidden;

    // 隐藏滚动条
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
        display: none;
    }

    .content {
        padding: 20px 16px;

        .user-info {
            background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
            border-radius: 20px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(37, 99, 235, 0.15);
            color: white;

            .user-header {
                display: flex;
                align-items: center;
                margin-bottom: 24px;

                .user-details {
                    flex: 1;
                    margin-left: 20px;

                    .username {
                        font-size: 20px;
                        font-weight: 700;
                        color: white;
                        margin-bottom: 6px;
                    }

                    .user-level {
                        font-size: 14px;
                        color: rgba(255, 255, 255, 0.9);
                        margin-bottom: 4px;
                        font-weight: 600;
                        background: rgba(255, 255, 255, 0.2);
                        padding: 4px 12px;
                        border-radius: 12px;
                        display: inline-block;
                    }

                    .user-id {
                        font-size: 12px;
                        color: rgba(255, 255, 255, 0.7);
                        font-weight: 500;
                    }
                }

                .van-icon {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 24px;
                    background: rgba(255, 255, 255, 0.2);
                    padding: 8px;
                    border-radius: 12px;
                }
            }

            .user-stats {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 16px;
                padding: 20px 0;
                backdrop-filter: blur(10px);

                .stat-item {
                    text-align: center;

                    .stat-number {
                        font-size: 22px;
                        font-weight: 700;
                        color: white;
                        margin-bottom: 6px;
                    }

                    .stat-label {
                        font-size: 12px;
                        color: rgba(255, 255, 255, 0.8);
                        font-weight: 600;
                    }
                }
            }
        }

        .menu-section,
        .settings-section {
            margin-bottom: 24px;

            ::v-deep .van-cell-group {
                background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                border-radius: 16px;
                box-shadow: 0 4px 20px rgba(37, 99, 235, 0.08);
                border: 1px solid rgba(37, 99, 235, 0.05);
                overflow: hidden;

                .van-cell {
                    background: transparent;
                    padding: 18px 20px;
                    transition: all 0.3s ease;

                    &:not(:last-child) {
                        border-bottom: 1px solid rgba(37, 99, 235, 0.06);
                    }

                    &:active {
                        background-color: rgba(37, 99, 235, 0.02);
                    }

                    .van-cell__title {
                        color: $text-primary;
                        font-weight: 600;
                        font-size: 16px;
                    }

                    .van-cell__value {
                        color: $text-secondary;
                        font-weight: 500;
                    }

                    .van-icon {
                        color: $primary-blue;
                        font-size: 20px;
                        margin-right: 12px;
                    }
                }
            }
        }

        .logout-section {
            margin-top: 40px;

            .van-button {
                border-radius: 16px;
                height: 50px;
                font-size: 16px;
                font-weight: 600;
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                border: none;
                box-shadow: 0 4px 20px rgba(239, 68, 68, 0.2);
                transition: all 0.3s ease;

                &:active {
                    transform: scale(0.98);
                    box-shadow: 0 8px 32px rgba(239, 68, 68, 0.3);
                }
            }
        }
    }
}
</style>
