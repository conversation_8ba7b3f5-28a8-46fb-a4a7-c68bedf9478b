<template>
    <div class="profile-page">
        <van-nav-bar title="个人中心" fixed placeholder />

        <div class="content">
            <!-- 用户信息 -->
            <div class="user-info">
                <div class="user-header">
                    <van-image
                        round
                        width="60"
                        height="60"
                        :src="userInfo.avatar"
                        @click="editAvatar"
                    />
                    <div class="user-details">
                        <div class="username">{{ userInfo.name }}</div>
                        <div class="user-level">{{ userInfo.level }}</div>
                        <div class="user-id">ID: {{ userInfo.id }}</div>
                    </div>
                    <van-icon name="edit" @click="editProfile" />
                </div>

                <!-- 学习数据 -->
                <div class="user-stats">
                    <van-grid :column-num="4" :border="false">
                        <van-grid-item>
                            <div class="stat-item">
                                <div class="stat-number">{{ userStats.courses }}</div>
                                <div class="stat-label">课程</div>
                            </div>
                        </van-grid-item>
                        <van-grid-item>
                            <div class="stat-item">
                                <div class="stat-number">{{ userStats.certificates }}</div>
                                <div class="stat-label">证书</div>
                            </div>
                        </van-grid-item>
                        <van-grid-item>
                            <div class="stat-item">
                                <div class="stat-number">{{ userStats.points }}</div>
                                <div class="stat-label">积分</div>
                            </div>
                        </van-grid-item>
                        <van-grid-item>
                            <div class="stat-item">
                                <div class="stat-number">{{ userStats.days }}</div>
                                <div class="stat-label">学习天数</div>
                            </div>
                        </van-grid-item>
                    </van-grid>
                </div>
            </div>

            <!-- 功能菜单 -->
            <div class="menu-section">
                <van-cell-group>
                    <van-cell
                        v-for="menu in menuList"
                        :key="menu.id"
                        :title="menu.title"
                        :icon="menu.icon"
                        :value="menu.value"
                        is-link
                        @click="handleMenuClick(menu)"
                    />
                </van-cell-group>
            </div>

            <!-- 设置菜单 -->
            <div class="settings-section">
                <van-cell-group>
                    <van-cell
                        v-for="setting in settingsList"
                        :key="setting.id"
                        :title="setting.title"
                        :icon="setting.icon"
                        is-link
                        @click="handleSettingClick(setting)"
                    />
                </van-cell-group>
            </div>

            <!-- 退出登录 -->
            <div class="logout-section">
                <van-button type="danger" block @click="logout"> 退出登录 </van-button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ProfilePage',
    data() {
        return {
            userInfo: {
                id: '123456',
                name: '张医生',
                level: '高级学员',
                avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
            },
            userStats: {
                courses: 12,
                certificates: 3,
                points: 2580,
                days: 45
            },
            menuList: [
                {
                    id: 1,
                    title: '我的课程',
                    icon: 'play-circle-o',
                    value: '',
                    path: '/my-courses'
                },
                {
                    id: 2,
                    title: '我的证书',
                    icon: 'certificate',
                    value: '',
                    path: '/my-certificates'
                },
                {
                    id: 3,
                    title: '学习记录',
                    icon: 'records',
                    value: '',
                    path: '/study-records'
                },
                {
                    id: 4,
                    title: '收藏夹',
                    icon: 'star-o',
                    value: '',
                    path: '/favorites'
                },
                {
                    id: 5,
                    title: '积分商城',
                    icon: 'gift-o',
                    value: '2580积分',
                    path: '/points-mall'
                }
            ],
            settingsList: [
                {
                    id: 1,
                    title: '账号设置',
                    icon: 'setting-o',
                    path: '/account-settings'
                },
                {
                    id: 2,
                    title: '消息通知',
                    icon: 'bell',
                    path: '/notifications'
                },
                {
                    id: 3,
                    title: '隐私设置',
                    icon: 'lock',
                    path: '/privacy-settings'
                },
                {
                    id: 4,
                    title: '帮助中心',
                    icon: 'question-o',
                    path: '/help'
                },
                {
                    id: 5,
                    title: '关于我们',
                    icon: 'info-o',
                    path: '/about'
                }
            ]
        }
    },
    methods: {
        editAvatar() {
            console.log('编辑头像')
            // 实现头像编辑功能
        },
        editProfile() {
            console.log('编辑个人信息')
            this.$router.push('/edit-profile')
        },
        handleMenuClick(menu) {
            console.log('点击菜单:', menu.title)
            this.$router.push(menu.path)
        },
        handleSettingClick(setting) {
            console.log('点击设置:', setting.title)
            this.$router.push(setting.path)
        },
        logout() {
            this.$dialog
                .confirm({
                    title: '确认退出',
                    message: '确定要退出登录吗？'
                })
                .then(() => {
                    // 清除用户信息
                    localStorage.removeItem('token')
                    localStorage.removeItem('userInfo')
                    // 跳转到登录页
                    this.$router.replace('/')
                })
                .catch(() => {
                    // 取消退出
                })
        }
    }
}
</script>

<style lang="scss" scoped>
.profile-page {
    background-color: #f7f8fa;
    min-height: 100vh;

    .content {
        padding: 16px;

        .user-info {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;

            .user-header {
                display: flex;
                align-items: center;
                margin-bottom: 20px;

                .user-details {
                    flex: 1;
                    margin-left: 16px;

                    .username {
                        font-size: 18px;
                        font-weight: bold;
                        color: #323233;
                        margin-bottom: 4px;
                    }

                    .user-level {
                        font-size: 14px;
                        color: #1989fa;
                        margin-bottom: 4px;
                    }

                    .user-id {
                        font-size: 12px;
                        color: #969799;
                    }
                }

                .van-icon {
                    color: #969799;
                    font-size: 20px;
                }
            }

            .user-stats {
                .stat-item {
                    text-align: center;

                    .stat-number {
                        font-size: 18px;
                        font-weight: bold;
                        color: #323233;
                        margin-bottom: 4px;
                    }

                    .stat-label {
                        font-size: 12px;
                        color: #969799;
                    }
                }
            }
        }

        .menu-section,
        .settings-section {
            margin-bottom: 16px;

            ::v-deep .van-cell-group {
                background-color: #fff;
                border-radius: 8px;
            }
        }

        .logout-section {
            margin-top: 32px;

            .van-button {
                border-radius: 8px;
            }
        }
    }
}
</style>
