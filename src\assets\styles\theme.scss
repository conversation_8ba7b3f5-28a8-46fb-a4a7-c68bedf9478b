// 蓝白现代化主题色彩系统
:root {
  // 主色调 - 蓝色系
  --primary-blue: #2563eb;
  --light-blue: #3b82f6;
  --lighter-blue: #60a5fa;
  --lightest-blue: #dbeafe;
  --dark-blue: #1d4ed8;
  
  // 背景色
  --background-primary: #ffffff;
  --background-secondary: #f8fafc;
  --background-tertiary: #f1f5f9;
  
  // 文字颜色
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-tertiary: #94a3b8;
  --text-light: #cbd5e1;
  
  // 边框颜色
  --border-light: rgba(37, 99, 235, 0.05);
  --border-medium: rgba(37, 99, 235, 0.1);
  --border-strong: rgba(37, 99, 235, 0.2);
  
  // 阴影
  --shadow-sm: 0 2px 8px rgba(37, 99, 235, 0.05);
  --shadow-md: 0 4px 20px rgba(37, 99, 235, 0.08);
  --shadow-lg: 0 8px 32px rgba(37, 99, 235, 0.12);
  --shadow-xl: 0 12px 48px rgba(37, 99, 235, 0.15);
  
  // 圆角
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

// 全局样式重置和基础样式
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color: var(--text-primary);
  background-color: var(--background-secondary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 通用组件样式覆盖
.van-nav-bar {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--light-blue) 100%) !important;
  
  .van-nav-bar__title {
    color: white !important;
    font-weight: 600 !important;
    font-size: 18px !important;
  }
  
  .van-icon {
    color: white !important;
  }
}

.van-button--primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--light-blue) 100%) !important;
  border: none !important;
  border-radius: var(--radius-md) !important;
  font-weight: 600 !important;
  box-shadow: var(--shadow-md) !important;
  transition: all 0.3s ease !important;
  
  &:active {
    transform: scale(0.98) !important;
    box-shadow: var(--shadow-lg) !important;
  }
}

.van-card {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-md) !important;
  border: 1px solid var(--border-light) !important;
  overflow: hidden !important;
  
  .van-card__header {
    background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-tertiary) 100%) !important;
  }
  
  .van-card__title {
    color: var(--text-primary) !important;
    font-weight: 700 !important;
  }
  
  .van-card__desc {
    color: var(--text-secondary) !important;
    font-weight: 500 !important;
  }
  
  .van-card__price {
    color: var(--primary-blue) !important;
    font-weight: 700 !important;
  }
}

.van-cell {
  background: var(--background-primary) !important;
  border-radius: var(--radius-md) !important;
  transition: all 0.3s ease !important;
  
  &:active {
    background: rgba(37, 99, 235, 0.02) !important;
  }
  
  .van-cell__title {
    color: var(--text-primary) !important;
    font-weight: 600 !important;
  }
  
  .van-cell__value {
    color: var(--text-secondary) !important;
    font-weight: 500 !important;
  }
  
  .van-cell__label {
    color: var(--text-tertiary) !important;
  }
}

.van-field {
  border-radius: var(--radius-md) !important;
  
  .van-field__control {
    color: var(--text-primary) !important;
    font-weight: 500 !important;
    
    &::placeholder {
      color: var(--text-secondary) !important;
    }
  }
}

.van-search {
  .van-search__content {
    background: var(--background-primary) !important;
    border-radius: var(--radius-lg) !important;
    box-shadow: var(--shadow-sm) !important;
    border: 1px solid var(--border-light) !important;
  }
}

.van-tabs {
  .van-tab {
    color: var(--text-secondary) !important;
    font-weight: 600 !important;
    
    &--active {
      color: var(--primary-blue) !important;
    }
  }
  
  .van-tabs__line {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--light-blue) 100%) !important;
    height: 3px !important;
    border-radius: 2px !important;
  }
}

.van-tag {
  border-radius: var(--radius-sm) !important;
  font-weight: 600 !important;
  
  &--primary {
    background: var(--primary-blue) !important;
    color: white !important;
  }
  
  &--success {
    background: #10b981 !important;
    color: white !important;
  }
  
  &--warning {
    background: #f59e0b !important;
    color: white !important;
  }
  
  &--danger {
    background: #ef4444 !important;
    color: white !important;
  }
}

.van-progress {
  .van-progress__portion {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--light-blue) 100%) !important;
  }
}

// 通用工具类
.gradient-bg {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--light-blue) 100%);
}

.card-style {
  background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-tertiary) 100%);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-blue {
  color: var(--primary-blue);
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

// 响应式设计
@media (max-width: 768px) {
  :root {
    --spacing-md: 12px;
    --spacing-lg: 20px;
    --spacing-xl: 28px;
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--background-tertiary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--lighter-blue);
  border-radius: 3px;
  
  &:hover {
    background: var(--light-blue);
  }
}
