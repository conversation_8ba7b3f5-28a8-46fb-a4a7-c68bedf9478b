<template>
    <div class="courses-page">
        <van-nav-bar title="课程中心" fixed placeholder />

        <div class="content">
            <!-- 搜索栏 -->
            <div class="search-section">
                <van-search
                    v-model="searchValue"
                    placeholder="搜索课程"
                    @search="onSearch"
                    @clear="onClear"
                />
            </div>

            <!-- 分类标签 -->
            <div class="category-section">
                <van-tabs v-model="activeCategory" @change="onCategoryChange">
                    <van-tab
                        v-for="category in categories"
                        :key="category.id"
                        :title="category.name"
                    />
                </van-tabs>
            </div>

            <!-- 课程列表 -->
            <div class="course-list">
                <van-list
                    v-model="loading"
                    :finished="finished"
                    finished-text="没有更多了"
                    @load="onLoad"
                >
                    <div
                        v-for="course in courseList"
                        :key="course.id"
                        class="course-item"
                        @click="goToCourseDetail(course)"
                    >
                        <van-card
                            :num="course.lessonCount + '课时'"
                            :price="course.price === 0 ? '免费' : '¥' + course.price"
                            :desc="course.description"
                            :title="course.title"
                            :thumb="course.cover"
                        >
                            <template #tags>
                                <van-tag v-if="course.isNew" type="danger">新课</van-tag>
                                <van-tag v-if="course.isHot" type="warning">热门</van-tag>
                            </template>
                            <template #footer>
                                <van-button size="mini" type="primary">立即学习</van-button>
                            </template>
                        </van-card>
                    </div>
                </van-list>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'CoursesPage',
    data() {
        return {
            searchValue: '',
            activeCategory: 0,
            loading: false,
            finished: false,
            categories: [
                { id: 0, name: '全部' },
                { id: 1, name: '基础医学' },
                { id: 2, name: '临床医学' },
                { id: 3, name: '护理学' },
                { id: 4, name: '药学' }
            ],
            courseList: [
                {
                    id: 1,
                    title: '人体解剖学基础',
                    description: '系统学习人体各系统的解剖结构',
                    cover: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    price: 0,
                    lessonCount: 24,
                    isNew: true,
                    isHot: false
                },
                {
                    id: 2,
                    title: '内科学精讲',
                    description: '内科常见疾病的诊断与治疗',
                    cover: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    price: 199,
                    lessonCount: 36,
                    isNew: false,
                    isHot: true
                }
            ]
        }
    },
    methods: {
        onSearch(value) {
            console.log('搜索:', value)
            // 实现搜索逻辑
        },
        onClear() {
            this.searchValue = ''
            // 清空搜索结果
        },
        onCategoryChange(index) {
            console.log('分类切换:', this.categories[index].name)
            // 根据分类筛选课程
        },
        onLoad() {
            // 模拟加载更多数据
            setTimeout(() => {
                this.loading = false
                this.finished = true
            }, 1000)
        },
        goToCourseDetail(course) {
            console.log('进入课程详情:', course.title)
            // 跳转到课程详情页
            this.$router.push(`/course-detail/${course.id}`)
        }
    }
}
</script>

<style lang="scss" scoped>
.courses-page {
    background-color: #f7f8fa;
    min-height: 100vh;

    .content {
        padding: 16px;

        .search-section {
            margin-bottom: 16px;
        }

        .category-section {
            margin-bottom: 16px;
            background-color: #fff;
            border-radius: 8px;
        }

        .course-list {
            .course-item {
                margin-bottom: 16px;

                ::v-deep .van-card {
                    background-color: #fff;
                    border-radius: 8px;
                    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
                }
            }
        }
    }
}
</style>
