<template>
    <div class="courses-page">
        <van-nav-bar title="课程中心" fixed placeholder />

        <div class="content">
            <!-- 搜索栏 -->
            <div class="search-section">
                <van-search
                    v-model="searchValue"
                    placeholder="搜索课程"
                    @search="onSearch"
                    @clear="onClear"
                />
            </div>

            <!-- 分类标签 -->
            <div class="category-section">
                <van-tabs v-model="activeCategory" @change="onCategoryChange">
                    <van-tab
                        v-for="category in categories"
                        :key="category.id"
                        :title="category.name"
                    />
                </van-tabs>
            </div>

            <!-- 课程列表 -->
            <div class="course-list">
                <van-list
                    v-model="loading"
                    :finished="finished"
                    finished-text="没有更多了"
                    @load="onLoad"
                >
                    <div
                        v-for="course in courseList"
                        :key="course.id"
                        class="course-item"
                        @click="goToCourseDetail(course)"
                    >
                        <van-card
                            :num="course.lessonCount + '课时'"
                            :price="course.price === 0 ? '免费' : '¥' + course.price"
                            :desc="course.description"
                            :title="course.title"
                            :thumb="course.cover"
                        >
                            <template #tags>
                                <van-tag v-if="course.isNew" type="danger">新课</van-tag>
                                <van-tag v-if="course.isHot" type="warning">热门</van-tag>
                            </template>
                            <template #footer>
                                <van-button size="mini" type="primary">立即学习</van-button>
                            </template>
                        </van-card>
                    </div>
                </van-list>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'CoursesPage',
    data() {
        return {
            searchValue: '',
            activeCategory: 0,
            loading: false,
            finished: false,
            categories: [
                { id: 0, name: '全部' },
                { id: 1, name: '基础医学' },
                { id: 2, name: '临床医学' },
                { id: 3, name: '护理学' },
                { id: 3, name: '护理学' },
                { id: 3, name: '护理学' },
                { id: 3, name: '护理学' },
                { id: 3, name: '护理学' },
                { id: 4, name: '药学' }
            ],
            courseList: [
                {
                    id: 1,
                    title: '人体解剖学基础',
                    description: '系统学习人体各系统的解剖结构',
                    cover: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    price: 0,
                    lessonCount: 24,
                    isNew: true,
                    isHot: false
                },
                {
                    id: 2,
                    title: '内科学精讲',
                    description: '内科常见疾病的诊断与治疗',
                    cover: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    price: 199,
                    lessonCount: 36,
                    isNew: false,
                    isHot: true
                }
            ]
        }
    },
    methods: {
        onSearch(value) {
            console.log('搜索:', value)
            // 实现搜索逻辑
        },
        onClear() {
            this.searchValue = ''
            // 清空搜索结果
        },
        onCategoryChange(index) {
            console.log('分类切换:', this.categories[index].name)
            // 根据分类筛选课程
        },
        onLoad() {
            // 模拟加载更多数据
            setTimeout(() => {
                this.loading = false
                this.finished = true
            }, 1000)
        },
        goToCourseDetail(course) {
            console.log('进入课程详情:', course.title)
            // 跳转到课程详情页
            this.$router.push(`/course-detail/${course.id}`)
        }
    }
}
</script>

<style lang="scss" scoped>
// 蓝白主题色彩变量
$primary-blue: #2563eb;
$light-blue: #3b82f6;
$lighter-blue: #60a5fa;
$lightest-blue: #dbeafe;
$background-blue: #f8fafc;
$white: #ffffff;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-light: #94a3b8;

.courses-page {
    background: linear-gradient(180deg, $background-blue 0%, #ffffff 100%);
    min-height: 100vh;
    overflow-x: hidden;

    // 隐藏滚动条
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
        display: none;
    }

    .content {
        padding: 20px 16px;

        .search-section {
            margin-bottom: 20px;

            ::v-deep .van-search {
                background: linear-gradient(135deg, $white 0%, #f1f5f9 100%);
                border-radius: 16px;
                box-shadow: 0 4px 20px rgba(37, 99, 235, 0.08);
                border: 1px solid rgba(37, 99, 235, 0.05);

                .van-search__content {
                    background: transparent;
                    border-radius: 16px;
                }

                input {
                    color: $text-primary;
                    font-weight: 500;

                    &::placeholder {
                        color: $text-secondary;
                    }
                }
            }
        }

        .category-section {
            margin-bottom: 24px;
            background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(37, 99, 235, 0.08);
            border: 1px solid rgba(37, 99, 235, 0.05);
            overflow: hidden;

            ::v-deep .van-tabs {
                .van-tabs__wrap {
                    background: transparent;
                }

                .van-tabs__nav {
                    background: transparent;
                }

                .van-tab {
                    color: $text-secondary;
                    font-weight: 600;

                    &--active {
                        color: $primary-blue;
                    }
                }

                .van-tabs__line {
                    background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                    height: 3px;
                    border-radius: 2px;
                }
            }
        }

        .course-list {
            .course-item {
                margin-bottom: 20px;

                ::v-deep .van-card {
                    background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                    border-radius: 16px;
                    box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
                    border: 1px solid rgba(37, 99, 235, 0.05);
                    overflow: hidden;
                    transition: all 0.3s ease;

                    &:active {
                        transform: scale(0.98);
                        box-shadow: 0 8px 32px rgba(37, 99, 235, 0.15);
                    }

                    .van-card__header {
                        padding: 16px;
                    }

                    .van-card__title {
                        color: $text-primary;
                        font-weight: 700;
                        font-size: 16px;
                    }

                    .van-card__desc {
                        color: $text-secondary;
                        font-weight: 500;
                        margin-top: 6px;
                    }

                    .van-card__price {
                        color: $primary-blue;
                        font-weight: 700;
                        font-size: 16px;
                    }

                    .van-card__num {
                        color: $text-light;
                        font-weight: 500;
                        font-size: 12px;
                    }

                    .van-card__footer {
                        padding: 16px;

                        .van-button {
                            background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                            border: none;
                            border-radius: 12px;
                            font-weight: 600;
                            box-shadow: 0 4px 16px rgba(37, 99, 235, 0.2);
                        }
                    }

                    .van-tag {
                        font-weight: 600;
                        border-radius: 8px;
                        margin-right: 6px;
                    }
                }
            }
        }
    }
}
</style>
