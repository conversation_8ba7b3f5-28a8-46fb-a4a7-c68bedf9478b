<template>
    <div class="login-page">
        <!-- 背景装饰 -->
        <div class="bg-decoration">
            <div class="circle circle-1"></div>
            <div class="circle circle-2"></div>
            <div class="circle circle-3"></div>
        </div>

        <!-- 主要内容 -->
        <div class="login-container">
            <!-- 头部信息 -->
            <div class="header-section">
                <div class="logo-container">
                    <div class="logo-icon">
                        <van-icon name="medical-o" />
                    </div>
                </div>
                <h1 class="app-title">在线医疗培训系统</h1>
                <p class="app-subtitle">专业医疗技能提升平台</p>
            </div>
            <!-- 表单 -->
            <div class="form-section">
                <div class="form-container" v-show="type === 'login'">
                    <!-- 登录方式切换 -->
                    <div class="login-tabs">
                        <div
                            class="tab-item"
                            :class="{ active: loginType === 'password' }"
                            @click="loginType = 'password'"
                        >
                            密码登录
                        </div>
                        <div
                            class="tab-item"
                            :class="{ active: loginType === 'sms' }"
                            @click="loginType = 'sms'"
                        >
                            验证码登录
                        </div>
                    </div>

                    <!-- 密码登录表单 -->
                    <div v-if="loginType === 'password'" class="form-content">
                        <div class="input-group">
                            <div class="input-label">用户名/手机号</div>
                            <van-field
                                v-model="loginForm.username"
                                placeholder="请输入用户名/手机号"
                                left-icon="user-o"
                                class="custom-field"
                                clearable
                            />
                        </div>

                        <div class="input-group">
                            <div class="input-label">密码</div>
                            <van-field
                                v-model="loginForm.password"
                                :type="showPassword ? 'text' : 'password'"
                                placeholder="请输入密码"
                                left-icon="lock"
                                :right-icon="showPassword ? 'eye-o' : 'closed-eye'"
                                @click-right-icon="showPassword = !showPassword"
                                class="custom-field"
                            />
                        </div>

                        <div class="form-options">
                            <van-checkbox v-model="rememberMe" shape="square">
                                记住密码
                            </van-checkbox>
                            <span class="forgot-password" @click="handleForgotPassword">
                                忘记密码？
                            </span>
                        </div>
                    </div>

                    <!-- 验证码登录表单 -->
                    <div v-if="loginType === 'sms'" class="form-content">
                        <div class="input-group">
                            <div class="input-label">手机号</div>
                            <van-field
                                v-model="smsForm.phone"
                                placeholder="请输入手机号"
                                left-icon="phone-o"
                                class="custom-field"
                                clearable
                            />
                        </div>

                        <div class="input-group">
                            <div class="input-label">验证码</div>
                            <van-field
                                v-model="smsForm.code"
                                placeholder="请输入验证码"
                                left-icon="shield-o"
                                class="custom-field"
                            >
                                <template #button>
                                    <van-button
                                        size="small"
                                        type="primary"
                                        :disabled="countdown > 0"
                                        @click="sendSmsCode"
                                        class="sms-button"
                                    >
                                        {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}
                                    </van-button>
                                </template>
                            </van-field>
                        </div>
                    </div>

                    <!-- 登录按钮 -->
                    <div class="login-actions">
                        <van-button
                            type="primary"
                            block
                            round
                            :loading="loginLoading"
                            @click="handleLogin"
                            class="login-button"
                        >
                            {{ loginLoading ? '登录中...' : '立即登录' }}
                        </van-button>
                    </div>

                    <!-- 快速注册 -->
                    <div class="register-link">
                        还没有账号？
                        <span @click="changetype" class="link-text"> 立即注册 </span>
                    </div>
                </div>
                <div class="form-container" v-if="type === 'register'">
                    <div class="form-content">
                        <div class="input-group">
                            <div class="input-label">手机号</div>
                            <van-field
                                v-model="smsForm.phone"
                                placeholder="请输入手机号"
                                left-icon="phone-o"
                                class="custom-field"
                                clearable
                            />
                        </div>

                        <div class="input-group">
                            <div class="input-label">验证码</div>
                            <van-field
                                v-model="smsForm.code"
                                placeholder="请输入验证码"
                                left-icon="shield-o"
                                class="custom-field"
                            >
                                <template #button>
                                    <van-button
                                        size="small"
                                        type="primary"
                                        :disabled="countdown > 0"
                                        @click="sendSmsCode"
                                        class="sms-button"
                                    >
                                        {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}
                                    </van-button>
                                </template>
                            </van-field>
                        </div>
                        <div class="input-group">
                            <div class="input-label">设置密码</div>
                            <van-field
                                v-model="loginForm.password"
                                :type="showPassword ? 'text' : 'password'"
                                placeholder="请输入密码"
                                left-icon="lock"
                                :right-icon="showPassword ? 'eye-o' : 'closed-eye'"
                                @click-right-icon="showPassword = !showPassword"
                                class="custom-field"
                            />
                        </div>
                        <div class="input-group">
                            <div class="input-label">确认密码</div>
                            <van-field
                                v-model="loginForm.password"
                                :type="showPassword ? 'text' : 'password'"
                                placeholder="请输入密码"
                                left-icon="lock"
                                :right-icon="showPassword ? 'eye-o' : 'closed-eye'"
                                @click-right-icon="showPassword = !showPassword"
                                class="custom-field"
                            />
                        </div>
                    </div>
                    <!-- 注册按钮 -->
                    <div class="login-actions">
                        <van-button
                            type="primary"
                            block
                            round
                            :loading="loginLoading"
                            @click="handleLogin"
                            class="login-button"
                        >
                            {{ loginLoading ? '注册中...' : '立即注册' }}
                        </van-button>
                    </div>
                    <!-- 返回登录 -->
                    <div class="register-link">
                        已经有账号？
                        <span @click="changetype" class="link-text"> 立即登录 </span>
                    </div>
                </div>
            </div>

            <!-- 底部协议 -->
            <div class="footer-section" v-if="type === 'login'">
                <p class="agreement-text">
                    登录即表示同意
                    <span class="link-text" @click="showUserAgreement">《用户协议》</span>
                    和
                    <span class="link-text" @click="showPrivacyPolicy">《隐私政策》</span>
                </p>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'LoginPage',
    data() {
        return {
            type: 'login',
            loginType: 'password', // 'password' | 'sms'
            showPassword: false,
            rememberMe: false,
            loginLoading: false,
            countdown: 0,
            regCountdown: 0,
            showRegisterDialog: false,

            // 密码登录表单
            loginForm: {
                username: '',
                password: ''
            },

            // 短信登录表单
            smsForm: {
                phone: '',
                code: ''
            },

            // 注册表单
            registerForm: {
                phone: '',
                code: '',
                password: ''
            }
        }
    },
    methods: {
        changetype() {
            this.type = this.type === 'login' ? 'register' : 'login'
        },
        // 处理登录
        async handleLogin() {
            try {
                this.loginLoading = true

                if (this.loginType === 'password') {
                    if (!this.loginForm.username || !this.loginForm.password) {
                        this.$toast('请填写完整的登录信息')
                        return
                    }
                    console.log('密码登录:', this.loginForm)
                } else {
                    if (!this.smsForm.phone || !this.smsForm.code) {
                        this.$toast('请填写完整的登录信息')
                        return
                    }
                    console.log('短信登录:', this.smsForm)
                }

                // 模拟登录成功
                await new Promise(resolve => setTimeout(resolve, 1500))
                this.$toast.success('登录成功')
                this.$router.push('/index')
            } catch (error) {
                this.$toast.fail('登录失败，请重试')
            } finally {
                this.loginLoading = false
            }
        },

        // 发送短信验证码
        async sendSmsCode() {
            if (!this.smsForm.phone) {
                this.$toast('请输入手机号')
                return
            }

            try {
                console.log('发送验证码到:', this.smsForm.phone)
                this.$toast.success('验证码已发送')

                this.countdown = 60
                const timer = setInterval(() => {
                    this.countdown--
                    if (this.countdown <= 0) {
                        clearInterval(timer)
                    }
                }, 1000)
            } catch (error) {
                this.$toast.fail('发送失败，请重试')
            }
        },

        // 发送注册验证码
        async sendRegisterCode() {
            if (!this.registerForm.phone) {
                this.$toast('请输入手机号')
                return
            }

            try {
                console.log('发送注册验证码到:', this.registerForm.phone)
                this.$toast.success('验证码已发送')

                this.regCountdown = 60
                const timer = setInterval(() => {
                    this.regCountdown--
                    if (this.regCountdown <= 0) {
                        clearInterval(timer)
                    }
                }, 1000)
            } catch (error) {
                this.$toast.fail('发送失败，请重试')
            }
        },

        // 处理注册
        async handleRegister() {
            if (
                !this.registerForm.phone ||
                !this.registerForm.code ||
                !this.registerForm.password
            ) {
                this.$toast('请填写完整的注册信息')
                return
            }

            try {
                console.log('注册:', this.registerForm)
                this.$toast.success('注册成功')
                this.showRegisterDialog = false

                // 注册成功后自动填入登录表单
                this.loginForm.username = this.registerForm.phone
                this.loginType = 'password'
            } catch (error) {
                this.$toast.fail('注册失败，请重试')
            }
        },

        // 忘记密码
        handleForgotPassword() {
            this.$toast('请联系管理员重置密码')
        },

        // 显示用户协议
        showUserAgreement() {
            this.$toast('用户协议')
        },

        // 显示隐私政策
        showPrivacyPolicy() {
            this.$toast('隐私政策')
        }
    }
}
</script>

<style lang="scss" scoped>
// 蓝白主题色彩变量
$primary-blue: #2563eb;
$light-blue: #3b82f6;
$lighter-blue: #60a5fa;
$lightest-blue: #dbeafe;
$background-blue: #f8fafc;
$white: #ffffff;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-light: #94a3b8;

.login-page {
    min-height: 100vh;
    background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;

    // 确保页面内滚动条隐藏
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
        display: none;
    }

    // 背景装饰
    .bg-decoration {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;

        .circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;

            &.circle-1 {
                width: 200px;
                height: 200px;
                top: 10%;
                left: -50px;
                animation-delay: 0s;
            }

            &.circle-2 {
                width: 150px;
                height: 150px;
                top: 60%;
                right: -30px;
                animation-delay: 2s;
            }

            &.circle-3 {
                width: 100px;
                height: 100px;
                bottom: 20%;
                left: 20%;
                animation-delay: 4s;
            }
        }
    }

    .login-container {
        width: 100%;
        max-width: 400px;
        z-index: 1;
    }
}
// 头部样式
.header-section {
    text-align: center;
    margin-bottom: 40px;

    .logo-container {
        margin-bottom: 24px;

        .logo-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(255, 255, 255, 0.1);

            .van-icon {
                font-size: 40px;
                color: white;
            }
        }
    }

    .app-title {
        font-size: 28px;
        font-weight: 700;
        color: white;
        margin: 0 0 8px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .app-subtitle {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
        font-weight: 500;
    }
}

// 表单样式
.form-section {
    .form-container {
        background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
        border-radius: 20px;
        padding: 32px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);

        .login-tabs {
            display: flex;
            background: rgba(37, 99, 235, 0.05);
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 24px;

            .tab-item {
                flex: 1;
                text-align: center;
                padding: 12px 16px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                color: $text-secondary;
                cursor: pointer;
                transition: all 0.3s ease;

                &.active {
                    background: $white;
                    color: $primary-blue;
                    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.1);
                }
            }
        }

        .form-content {
            .input-group {
                margin-bottom: 10px;

                .input-label {
                    font-size: 14px;
                    font-weight: 600;
                    color: $text-primary;
                    margin-bottom: 8px;
                }

                .custom-field {
                    ::v-deep .van-field__control {
                        background: rgba(248, 250, 252, 0.8);
                        border: 1px solid rgba(37, 99, 235, 0.1);
                        border-radius: 12px;
                        padding: 10px;
                        font-size: 16px;
                        color: $text-primary;
                        transition: all 0.3s ease;

                        &:focus {
                            border-color: $primary-blue;
                            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
                        }

                        &::placeholder {
                            color: $text-secondary;
                        }
                    }

                    ::v-deep .van-field__left-icon {
                        color: $primary-blue;
                        margin-right: 12px;
                        display: flex;
                        align-items: center;
                    }

                    ::v-deep .van-field__right-icon {
                        color: $text-secondary;
                    }
                }
            }

            .form-options {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 24px;

                ::v-deep .van-checkbox {
                    .van-checkbox__label {
                        color: $text-secondary;
                        font-size: 14px;
                        font-weight: 500;
                    }

                    .van-checkbox__icon {
                        border-color: rgba(37, 99, 235, 0.3);

                        &--checked {
                            background: $primary-blue;
                            border-color: $primary-blue;
                        }
                    }
                }

                .forgot-password {
                    color: $primary-blue;
                    font-size: 14px;
                    font-weight: 600;
                    cursor: pointer;
                    text-decoration: none;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }

        .login-actions {
            margin-bottom: 24px;

            .login-button {
                height: 50px;
                font-size: 16px;
                font-weight: 600;
                background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                border: none;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(37, 99, 235, 0.2);
                transition: all 0.3s ease;

                &:active {
                    transform: scale(0.98);
                    box-shadow: 0 8px 32px rgba(37, 99, 235, 0.3);
                }
            }
        }

        .register-link {
            text-align: center;
            margin-bottom: 24px;
            font-size: 14px;
            color: $text-secondary;

            .link-text {
                color: $primary-blue;
                font-weight: 600;
                cursor: pointer;
                text-decoration: none;

                &:hover {
                    text-decoration: underline;
                }
            }
        }

        .third-party-login {
            .divider {
                position: relative;
                text-align: center;
                margin: 24px 0;

                &::before {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: rgba(37, 99, 235, 0.1);
                }

                span {
                    background: $white;
                    padding: 0 16px;
                    font-size: 12px;
                    color: $text-light;
                    font-weight: 500;
                }
            }

            .social-buttons {
                display: flex;
                justify-content: center;
                gap: 24px;

                .social-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    cursor: pointer;
                    padding: 12px;
                    border-radius: 12px;
                    transition: all 0.3s ease;

                    &:hover {
                        background: rgba(37, 99, 235, 0.05);
                    }

                    .van-icon {
                        font-size: 24px;
                        color: $primary-blue;
                        margin-bottom: 4px;
                    }

                    span {
                        font-size: 12px;
                        color: $text-secondary;
                        font-weight: 500;
                    }
                }
            }
        }

        .sms-button {
            background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
            border: none;
            border-radius: 8px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(37, 99, 235, 0.2);
        }
    }
}

// 底部样式
.footer-section {
    text-align: center;
    margin-top: 24px;

    .agreement-text {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.5;
        margin: 0;

        .link-text {
            color: white;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}

// 注册弹窗样式
::v-deep .register-dialog {
    .van-dialog__content {
        padding: 24px;

        .register-form {
            .van-field {
                margin-bottom: 16px;

                .van-field__control {
                    border: 1px solid rgba(37, 99, 235, 0.1);
                    border-radius: 8px;
                    padding: 12px;
                }
            }
        }
    }
}

// 动画效果
@keyframes float {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

// 响应式设计
@media (max-width: 480px) {
    .login-page {
        padding: 16px;

        .login-container {
            max-width: 100%;
        }

        .header-section {
            margin-bottom: 32px;

            .app-title {
                font-size: 24px;
            }

            .app-subtitle {
                font-size: 14px;
            }
        }

        .form-section {
            .form-container {
                padding: 24px;
            }
        }
    }
}
</style>
