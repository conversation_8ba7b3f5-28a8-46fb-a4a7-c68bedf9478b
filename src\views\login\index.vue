<template>
    <div class="login-page">
        <div class="login_box">
            <div class="login_top">
                <!-- logo -->
                <div class="logo">
                    <!-- <img src="../assets/logo.png" alt="logo" /> -->
                </div>
                <!-- 名称 -->
                <div class="title">
                    <p>在线医疗培训系统</p>
                    <p>提升医疗专业技能的学习平台</p>
                </div>
            </div>
            <div class="login_content">
                <van-tabs
                    color="#1989fa"
                    background="#fff"
                    title-active-color="#1989fa"
                    active-color="#1989fa"
                >
                    <van-tab title="登录">
                        <van-tabs
                            v-model="active"
                            type="card"
                            color="#1989fa"
                            background="#fff"
                            title-active-color="#fff"
                            active-color="#fff"
                        >
                            <van-tab title="密码登录">
                                <van-form @submit="onSubmit">
                                    <div style="text-align: left; margin-bottom: 10px">
                                        用户名/手机号
                                    </div>
                                    <van-field
                                        class="bordered-field"
                                        left-icon="manager"
                                        v-model="username"
                                        name="用户名"
                                        placeholder="用户名"
                                    />
                                    <!-- 密码 -->
                                    <div style="text-align: left; margin-bottom: 10px">密码</div>
                                    <van-field
                                        class="bordered-field"
                                        v-model="password"
                                        :type="passwordVisible ? 'text' : 'password'"
                                        name="密码"
                                        placeholder="密码"
                                        :right-icon="passwordVisible ? 'eye-o' : 'closed-eye'"
                                        @click-right-icon="togglePasswordVisibility"
                                    />
                                </van-form>

                                <!-- 记住密码  忘记密码? -->
                                <div class="other-login">
                                    <van-row>
                                        <van-col span="12">
                                            <van-checkbox v-model="remember" shape="square"
                                                >记住密码</van-checkbox
                                            >
                                        </van-col>
                                    </van-row>
                                </div>
                            </van-tab>

                            <van-tab title="验证码登录">
                                <van-cell-group>
                                    <div style="text-align: left; margin-bottom: 10px">手机号</div>
                                    <van-field
                                        class="bordered-field"
                                        v-model="phone"
                                        placeholder="请输入手机号"
                                    />
                                    <div style="text-align: left; margin-bottom: 10px">验证码</div>
                                    <van-field
                                        class="bordered-field"
                                        v-model="code"
                                        placeholder="请输入验证码"
                                    >
                                        <template #button>
                                            <van-button
                                                style="position: absolute; right: 5px; top: -4px"
                                                size="small"
                                                type="info"
                                                >发送验证码</van-button
                                            >
                                        </template>
                                    </van-field>
                                </van-cell-group>
                            </van-tab>
                        </van-tabs>
                        <div class="login_button">
                            <van-button @click="login">登录 →</van-button>
                        </div>
                        <div class="bottom-tip">登录即表示您同意我们的服务条款和隐私政策</div>
                    </van-tab>
                    <van-tab title="注册">
                        <van-form @submit="onSubmit">
                            <div style="text-align: left; margin-bottom: 10px">手机号</div>
                            <van-field
                                class="bordered-field"
                                v-model="phone"
                                placeholder="请输入手机号"
                            />
                            <div style="text-align: left; margin-bottom: 10px">验证码</div>
                            <van-field
                                class="bordered-field"
                                v-model="code"
                                placeholder="请输入验证码"
                            >
                                <template #button>
                                    <van-button
                                        style="position: absolute; right: 5px; top: -4px"
                                        size="small"
                                        type="info"
                                        >发送验证码</van-button
                                    >
                                </template>
                            </van-field>
                            <!-- 密码 -->
                            <div style="text-align: left; margin-bottom: 10px">设置密码</div>
                            <van-field
                                class="bordered-field"
                                v-model="password"
                                :type="passwordVisible ? 'text' : 'password'"
                                name="密码"
                                placeholder="密码"
                                :right-icon="passwordVisible ? 'eye-o' : 'closed-eye'"
                                @click-right-icon="togglePasswordVisibility"
                            />
                            <!-- 密码 -->
                            <div style="text-align: left; margin-bottom: 10px">确认密码</div>
                            <van-field
                                class="bordered-field"
                                v-model="confirmPassword"
                                :type="confirmPasswordVisible ? 'text' : 'password'"
                                name="确认密码"
                                placeholder="确认密码"
                                :right-icon="confirmPasswordVisible ? 'eye-o' : 'closed-eye'"
                                @click-right-icon="toggleConfirmPasswordVisibility"
                            />
                        </van-form>
                        <div class="bottom-tip">
                            <van-checkbox v-model="remember" shape="square"
                                >我已阅读并同意《用户协议》和《隐私政策》</van-checkbox
                            >
                        </div>
                        <div class="login_button">
                            <van-button @click="login">注册+</van-button>
                        </div>
                    </van-tab>
                </van-tabs>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'LoginPage',
    data() {
        return {
            type: 'login',
            active: '密码登录',
            phone: '',
            code: '',
            username: '',
            password: '',
            confirmPassword: '',
            remember: false,
            passwordVisible: false,
            confirmPasswordVisible: false
        }
    },
    methods: {
        login() {
            // 登录逻辑
        },
        changeType() {
            if (this.type === 'register') {
                this.type = 'login'
            } else {
                this.type = 'register'
            }
        },
        onSubmit() {
            // 表单提交逻辑
        },
        togglePasswordVisibility() {
            this.passwordVisible = !this.passwordVisible
        },
        toggleConfirmPasswordVisibility() {
            this.confirmPasswordVisible = !this.confirmPasswordVisible
        }
    }
}
</script>

<style lang="scss" scoped>
.login-page {
    display: flex;
    padding: 50px;
    background-color: #f9fafb;
}
.login_box {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .login_top {
        text-align: center;
        margin-bottom: 20px;

        .logo {
            img {
                width: 100px;
                height: 100px;
            }
        }

        .title {
            p {
                font-size: 20px;
                color: #333;
                margin: 5px 0;
            }
        }
    }

    .login_content {
        width: 100%;
        max-width: 400px;
        background-color: #fff;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 10px 30px -5px rgba(0, 0, 0, 0.1);

        .tab-switch {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #bababaca;
            span {
                padding: 5px 10px;
                cursor: pointer;
                font-size: 20px;
                font-weight: 400;
                color: #1989fa;

                &.active {
                    color: #1989fa;
                    border-bottom: 2px solid #1989fa;
                }
            }
        }

        // 为输入框添加边框样式
        ::v-deep .bordered-field {
            border: 1px solid #bababaca;
            border-radius: 8px;
            margin-bottom: 15px;

            &:hover {
                border-color: #1989fa;
            }

            &.van-field--focused {
                border-color: #1989fa;
            }
        }
        ::v-deep .van-tabs__wrap {
            margin-bottom: 20px;
        }
        ::v-deep .van-tabs--card > .van-tabs__wrap {
            height: 40px;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        ::v-deep .van-tabs__nav--card {
            height: 100%;
            border-radius: 10px;
            border: transparent;
            overflow: hidden;
        }
        .van-tabs {
            .van-tab__pane {
                padding: 10px 0;
            }
        }

        .login_button {
            margin-top: 20px;

            .van-button {
                width: 100%;
                border-radius: 8px;
                font-size: 16px;
                background-color: #1989fa;
                color: #fff;
            }

            .register {
                display: flex;
                justify-content: center;
                margin-top: 10px;

                span {
                    font-size: 14px;
                    color: #999;

                    &:last-child {
                        color: #1989fa;
                        cursor: pointer;
                        margin-left: 5px;
                    }
                }
            }
        }

        .other-login-methods {
            margin-top: 20px;
            text-align: center;

            div:first-child {
                font-size: 14px;
                color: #666;
                margin-bottom: 10px;
            }

            .icons {
                display: flex;
                justify-content: center;

                img {
                    width: 40px;
                    height: 40px;
                    margin: 0 10px;
                    border-radius: 50%;
                    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
                }
            }
        }
    }

    .bottom-tip {
        margin-top: 20px;
        text-align: center;
        font-size: 14px;
        color: #666;
    }
    ::v-deep .van-tabs__line {
        width: 50%;
    }
    ::v-deep .van-tab__text {
        font-size: 16px;
    }
}
</style>
