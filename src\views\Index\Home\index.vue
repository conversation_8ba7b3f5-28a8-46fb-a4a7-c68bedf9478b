<template>
    <div class="home-page">
        <van-nav-bar title="首页" fixed />
        <div class="content">
            <!-- 搜索栏 -->
            <div class="search-section">
                <van-search
                    v-model="searchValue"
                    placeholder="搜索课程、讲师、专业..."
                    @search="onSearch"
                    @clear="onClear"
                    shape="round"
                    background="#f7f8fa"
                />
            </div>

            <!-- 轮播图 -->
            <div class="banner-section">
                <van-swipe :autoplay="3000" indicator-color="white" class="banner-swipe">
                    <van-swipe-item v-for="banner in banners" :key="banner.id">
                        <div class="banner-item" @click="goToBanner(banner)">
                            <img :src="banner.image" :alt="banner.title" />
                            <div class="banner-overlay">
                                <div class="banner-title">{{ banner.title }}</div>
                                <div class="banner-subtitle">{{ banner.subtitle }}</div>
                            </div>
                        </div>
                    </van-swipe-item>
                </van-swipe>
            </div>

            <!-- 快捷入口 -->
            <div class="quick-entry">
                <van-grid :column-num="4" :border="false" class="entry-grid">
                    <van-grid-item
                        v-for="entry in quickEntries"
                        :key="entry.id"
                        @click="goToEntry(entry)"
                        class="entry-item"
                    >
                        <div class="entry-content">
                            <van-icon :name="entry.icon" class="entry-icon" />
                            <span class="entry-text">{{ entry.text }}</span>
                        </div>
                    </van-grid-item>
                </van-grid>
            </div>

            <!-- 分类课程菜单 -->
            <div class="category-menu">
                <div class="section-header">
                    <h3>课程分类</h3>
                    <span class="more-btn" @click="goToAllCategories">查看全部</span>
                </div>
                <van-grid :column-num="2" :border="false" class="category-grid">
                    <van-grid-item
                        v-for="category in categories"
                        :key="category.id"
                        @click="goToCategory(category)"
                        class="category-item"
                    >
                        <div class="category-content">
                            <div class="category-icon">
                                <van-icon :name="category.icon" />
                            </div>
                            <div class="category-info">
                                <div class="category-name">{{ category.name }}</div>
                                <div class="category-count">{{ category.courseCount }}门课程</div>
                            </div>
                        </div>
                    </van-grid-item>
                </van-grid>
            </div>

            <!-- 推荐课程 -->
            <div class="recommend-section">
                <div class="section-header">
                    <h3>推荐课程</h3>
                    <span class="more-btn" @click="goToAllCourses">更多课程</span>
                </div>
                <div class="course-list">
                    <div
                        v-for="course in recommendCourses"
                        :key="course.id"
                        class="course-card"
                        @click="goToCourse(course)"
                    >
                        <div class="course-image">
                            <van-image :src="course.cover" fit="cover" />
                            <div class="course-tags">
                                <van-tag v-if="course.isNew" type="danger" size="mini"
                                    >新课</van-tag
                                >
                                <van-tag v-if="course.isHot" type="warning" size="mini"
                                    >热门</van-tag
                                >
                                <van-tag v-if="course.isFree" type="success" size="mini"
                                    >免费</van-tag
                                >
                            </div>
                        </div>
                        <div class="course-info">
                            <div class="course-title">{{ course.title }}</div>
                            <div class="course-teacher">{{ course.teacher }}</div>
                            <div class="course-meta">
                                <span class="course-price">{{ getPriceText(course.price) }}</span>
                                <span class="course-students">{{ course.students }}人学习</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 学习动态 -->
            <div class="activity-section">
                <div class="section-header">
                    <h3>学习动态</h3>
                </div>
                <van-list class="activity-list">
                    <van-cell
                        v-for="activity in activities"
                        :key="activity.id"
                        :title="activity.title"
                        :label="activity.time"
                        :value="activity.type"
                        class="activity-item"
                    >
                        <template #icon>
                            <van-image round width="40" height="40" :src="activity.avatar" />
                        </template>
                    </van-cell>
                </van-list>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'HomePage',
    data() {
        return {
            searchValue: '',
            banners: [
                {
                    id: 1,
                    title: '新课程上线',
                    subtitle: '人体解剖学基础课程全新发布',
                    image: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    link: '/courses'
                },
                {
                    id: 2,
                    title: '免费学习周',
                    subtitle: '精选课程限时免费开放',
                    image: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    link: '/courses'
                },
                {
                    id: 3,
                    title: '专业认证',
                    subtitle: '完成学习获得权威认证证书',
                    image: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    link: '/certificates'
                }
            ],
            quickEntries: [
                { id: 1, icon: 'play-circle-o', text: '课程学习', path: '/courses' },
                { id: 2, icon: 'certificate', text: '考试认证', path: '/exams' },
                { id: 3, icon: 'records', text: '学习记录', path: '/study' },
                { id: 4, icon: 'question-o', text: '在线答疑', path: '/qa' }
            ],
            categories: [
                {
                    id: 1,
                    name: '基础医学',
                    icon: 'medical-o',
                    courseCount: 45,
                    description: '人体解剖、生理、病理等基础学科'
                },
                {
                    id: 2,
                    name: '临床医学',
                    icon: 'hospital-o',
                    courseCount: 68,
                    description: '内科、外科、妇产科等临床学科'
                },
                {
                    id: 3,
                    name: '护理学',
                    icon: 'user-circle-o',
                    courseCount: 32,
                    description: '基础护理、专科护理技能培训'
                },
                {
                    id: 4,
                    name: '药学',
                    icon: 'bag-o',
                    courseCount: 28,
                    description: '药理学、药剂学、临床药学'
                }
            ],
            recommendCourses: [
                {
                    id: 1,
                    title: '人体解剖学基础',
                    teacher: '张教授',
                    cover: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    price: 0,
                    students: 1234,
                    isNew: true,
                    isHot: false,
                    isFree: true
                },
                {
                    id: 2,
                    title: '内科学精讲',
                    teacher: '李主任',
                    cover: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    price: 199,
                    students: 856,
                    isNew: false,
                    isHot: true,
                    isFree: false
                },
                {
                    id: 3,
                    title: '护理学基础',
                    teacher: '王护士长',
                    cover: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    price: 0,
                    students: 2341,
                    isNew: false,
                    isHot: false,
                    isFree: true
                },
                {
                    id: 4,
                    title: '药理学概论',
                    teacher: '赵博士',
                    cover: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    price: 299,
                    students: 567,
                    isNew: true,
                    isHot: true,
                    isFree: false
                }
            ],
            activities: [
                {
                    id: 1,
                    title: '张医生完成了《人体解剖学基础》',
                    time: '2小时前',
                    type: '课程完成',
                    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
                },
                {
                    id: 2,
                    title: '李护士获得了护理学认证证书',
                    time: '5小时前',
                    type: '证书获得',
                    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
                },
                {
                    id: 3,
                    title: '王医生开始学习《内科学精讲》',
                    time: '1天前',
                    type: '开始学习',
                    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
                }
            ]
        }
    },
    methods: {
        onSearch(value) {
            console.log('搜索:', value)
            // 跳转到搜索结果页
            this.$router.push(`/search?q=${encodeURIComponent(value)}`)
        },
        onClear() {
            this.searchValue = ''
        },
        goToBanner(banner) {
            console.log('点击轮播图:', banner.title)
            this.$router.push(banner.link)
        },
        goToEntry(entry) {
            console.log('点击快捷入口:', entry.text)
            this.$router.push(entry.path)
        },
        goToAllCategories() {
            console.log('查看全部分类')
            this.$router.push('/categories')
        },
        goToCategory(category) {
            console.log('点击分类:', category.name)
            this.$router.push(`/courses?category=${category.id}`)
        },
        goToAllCourses() {
            console.log('查看更多课程')
            this.$router.push('/courses')
        },
        goToCourse(course) {
            console.log('点击课程:', course.title)
            this.$router.push(`/course-detail/${course.id}`)
        },
        getPriceText(price) {
            return price === 0 ? '免费' : `¥${price}`
        }
    }
}
</script>

<style lang="scss" scoped>
// 蓝白主题色彩变量
$primary-blue: #2563eb;
$light-blue: #3b82f6;
$lighter-blue: #60a5fa;
$lightest-blue: #dbeafe;
$background-blue: #f8fafc;
$white: #ffffff;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-light: #94a3b8;

.home-page {
    background: linear-gradient(180deg, $background-blue 0%, #ffffff 100%);
    min-height: 100vh;

    .content {
        padding-top: 20px;

        // 搜索栏样式
        .search-section {
            padding: 20px 16px;
            background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
            margin-bottom: 0;

            ::v-deep .van-search {
                background: transparent;

                .van-search__content {
                    background-color: rgba(255, 255, 255, 0.95);
                    border-radius: 25px;
                    box-shadow: 0 4px 20px rgba(37, 99, 235, 0.15);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                }

                .van-search__field {
                    background: transparent;
                }

                input {
                    color: $text-primary;

                    &::placeholder {
                        color: $text-secondary;
                    }
                }
            }
        }

        // 轮播图样式
        .banner-section {
            margin-bottom: 24px;
            padding-top: 16px;

            .banner-swipe {
                height: 200px;
                border-radius: 16px;
                overflow: hidden;
                margin: 0 16px;
                box-shadow: 0 8px 32px rgba(37, 99, 235, 0.12);

                .banner-item {
                    position: relative;
                    height: 100%;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }

                    .banner-overlay {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        background: linear-gradient(transparent, rgba(30, 41, 59, 0.8));
                        padding: 24px;
                        color: white;

                        .banner-title {
                            font-size: 20px;
                            font-weight: 700;
                            margin-bottom: 6px;
                            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                        }

                        .banner-subtitle {
                            font-size: 14px;
                            opacity: 0.95;
                            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                        }
                    }
                }
            }
        }

        // 快捷入口样式
        .quick-entry {
            background: linear-gradient(135deg, $white 0%, #f1f5f9 100%);
            margin: 0 16px 24px;
            border-radius: 16px;
            padding: 24px 0;
            box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
            border: 1px solid rgba(37, 99, 235, 0.05);

            .entry-grid {
                ::v-deep .van-grid-item__content {
                    padding: 20px 8px;
                    transition: all 0.3s ease;

                    &:active {
                        transform: scale(0.95);
                    }
                }
            }

            .entry-content {
                display: flex;
                flex-direction: column;
                align-items: center;

                .entry-icon {
                    font-size: 32px;
                    color: $primary-blue;
                    margin-bottom: 10px;
                    padding: 12px;
                    background: linear-gradient(
                        135deg,
                        $lightest-blue 0%,
                        rgba(219, 234, 254, 0.5) 100%
                    );
                    border-radius: 12px;
                    transition: all 0.3s ease;
                }

                .entry-text {
                    font-size: 13px;
                    color: $text-primary;
                    font-weight: 600;
                }
            }
        }

        // 分类菜单样式
        .category-menu {
            margin: 0 16px 24px;

            .category-grid {
                background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                border-radius: 16px;
                overflow: hidden;
                box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
                border: 1px solid rgba(37, 99, 235, 0.05);

                .category-item {
                    ::v-deep .van-grid-item__content {
                        padding: 24px 20px;
                        height: 120px;
                        transition: all 0.3s ease;

                        &:active {
                            transform: scale(0.98);
                            background-color: rgba(37, 99, 235, 0.02);
                        }
                    }

                    .category-content {
                        display: flex;
                        align-items: center;
                        height: 100%;

                        .category-icon {
                            width: 56px;
                            height: 56px;
                            background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                            border-radius: 16px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-right: 16px;
                            box-shadow: 0 4px 16px rgba(37, 99, 235, 0.2);

                            .van-icon {
                                font-size: 26px;
                                color: white;
                            }
                        }

                        .category-info {
                            flex: 1;

                            .category-name {
                                font-size: 16px;
                                font-weight: 700;
                                color: $text-primary;
                                margin-bottom: 6px;
                            }

                            .category-count {
                                font-size: 12px;
                                color: $text-secondary;
                                font-weight: 500;
                            }
                        }
                    }
                }
            }
        }

        // 推荐课程样式
        .recommend-section {
            margin: 0 16px 24px;

            .course-list {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;

                .course-card {
                    background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                    border-radius: 16px;
                    overflow: hidden;
                    box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
                    border: 1px solid rgba(37, 99, 235, 0.05);
                    transition: all 0.3s ease;

                    &:active {
                        transform: scale(0.96);
                        box-shadow: 0 8px 32px rgba(37, 99, 235, 0.15);
                    }

                    .course-image {
                        position: relative;
                        height: 110px;

                        ::v-deep .van-image {
                            width: 100%;
                            height: 100%;
                        }

                        .course-tags {
                            position: absolute;
                            top: 10px;
                            left: 10px;
                            display: flex;
                            gap: 6px;

                            ::v-deep .van-tag {
                                font-weight: 600;
                                border-radius: 8px;
                                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                            }
                        }
                    }

                    .course-info {
                        padding: 16px;

                        .course-title {
                            font-size: 15px;
                            font-weight: 700;
                            color: $text-primary;
                            margin-bottom: 6px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            line-height: 1.3;
                        }

                        .course-teacher {
                            font-size: 12px;
                            color: $text-secondary;
                            margin-bottom: 10px;
                            font-weight: 500;
                        }

                        .course-meta {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;

                            .course-price {
                                font-size: 15px;
                                font-weight: 700;
                                color: $primary-blue;
                            }

                            .course-students {
                                font-size: 11px;
                                color: $text-light;
                                font-weight: 500;
                            }
                        }
                    }
                }
            }
        }

        // 学习动态样式
        .activity-section {
            margin: 0 16px 24px;

            .activity-list {
                background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                border-radius: 16px;
                overflow: hidden;
                box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
                border: 1px solid rgba(37, 99, 235, 0.05);

                .activity-item {
                    ::v-deep .van-cell {
                        padding: 18px 20px;
                        transition: all 0.3s ease;

                        &:not(:last-child) {
                            border-bottom: 1px solid rgba(37, 99, 235, 0.06);
                        }

                        &:active {
                            background-color: rgba(37, 99, 235, 0.02);
                        }

                        .van-cell__title {
                            color: $text-primary;
                            font-weight: 600;
                        }

                        .van-cell__label {
                            color: $text-secondary;
                            font-weight: 500;
                        }

                        .van-cell__value {
                            color: $primary-blue;
                            font-weight: 600;
                            font-size: 12px;
                        }
                    }
                }
            }
        }

        // 通用区块标题样式
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding: 0 8px;

            h3 {
                font-size: 20px;
                font-weight: 700;
                color: $text-primary;
                margin: 0;
                position: relative;

                &::before {
                    content: '';
                    position: absolute;
                    left: -8px;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 4px;
                    height: 20px;
                    background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                    border-radius: 2px;
                }
            }

            .more-btn {
                font-size: 14px;
                color: $primary-blue;
                font-weight: 600;
                cursor: pointer;
                padding: 6px 12px;
                border-radius: 12px;
                background: rgba(37, 99, 235, 0.08);
                transition: all 0.3s ease;

                &:active {
                    background: rgba(37, 99, 235, 0.15);
                    transform: scale(0.95);
                }
            }
        }
    }
}
</style>
