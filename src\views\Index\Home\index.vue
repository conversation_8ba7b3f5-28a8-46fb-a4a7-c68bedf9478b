<template>
    <div class="home-page">
        <van-nav-bar title="首页" fixed />
        <div class="content">
            <!-- 搜索栏 -->
            <div class="search-section">
                <van-search
                    v-model="searchValue"
                    placeholder="搜索课程、讲师、专业..."
                    @search="onSearch"
                    @clear="onClear"
                    shape="round"
                    background="#f7f8fa"
                />
            </div>

            <!-- 轮播图 -->
            <div class="banner-section">
                <van-swipe :autoplay="3000" indicator-color="white" class="banner-swipe">
                    <van-swipe-item v-for="banner in banners" :key="banner.id">
                        <div class="banner-item" @click="goToBanner(banner)">
                            <img :src="banner.image" :alt="banner.title" />
                            <div class="banner-overlay">
                                <div class="banner-title">{{ banner.title }}</div>
                                <div class="banner-subtitle">{{ banner.subtitle }}</div>
                            </div>
                        </div>
                    </van-swipe-item>
                </van-swipe>
            </div>

            <!-- 快捷入口 -->
            <div class="quick-entry">
                <van-grid :column-num="4" :border="false" class="entry-grid">
                    <van-grid-item
                        v-for="entry in quickEntries"
                        :key="entry.id"
                        @click="goToEntry(entry)"
                        class="entry-item"
                    >
                        <div class="entry-content">
                            <van-icon :name="entry.icon" class="entry-icon" />
                            <span class="entry-text">{{ entry.text }}</span>
                        </div>
                    </van-grid-item>
                </van-grid>
            </div>

            <!-- 分类课程菜单 -->
            <div class="category-menu">
                <div class="section-header">
                    <h3>课程分类</h3>
                    <span class="more-btn" @click="goToAllCategories">查看全部</span>
                </div>
                <van-grid :column-num="2" :border="false" class="category-grid">
                    <van-grid-item
                        v-for="category in categories"
                        :key="category.id"
                        @click="goToCategory(category)"
                        class="category-item"
                    >
                        <div class="category-content">
                            <div class="category-icon">
                                <van-icon :name="category.icon" />
                            </div>
                            <div class="category-info">
                                <div class="category-name">{{ category.name }}</div>
                                <div class="category-count">{{ category.courseCount }}门课程</div>
                            </div>
                        </div>
                    </van-grid-item>
                </van-grid>
            </div>

            <!-- 推荐课程 -->
            <div class="recommend-section">
                <div class="section-header">
                    <h3>推荐课程</h3>
                    <span class="more-btn" @click="goToAllCourses">更多课程</span>
                </div>
                <div class="course-list">
                    <div
                        v-for="course in recommendCourses"
                        :key="course.id"
                        class="course-card"
                        @click="goToCourse(course)"
                    >
                        <div class="course-image">
                            <van-image :src="course.cover" fit="cover" />
                            <div class="course-tags">
                                <van-tag v-if="course.isNew" type="danger" size="mini"
                                    >新课</van-tag
                                >
                                <van-tag v-if="course.isHot" type="warning" size="mini"
                                    >热门</van-tag
                                >
                                <van-tag v-if="course.isFree" type="success" size="mini"
                                    >免费</van-tag
                                >
                            </div>
                        </div>
                        <div class="course-info">
                            <div class="course-title">{{ course.title }}</div>
                            <div class="course-teacher">{{ course.teacher }}</div>
                            <div class="course-meta">
                                <span class="course-price">{{ getPriceText(course.price) }}</span>
                                <span class="course-students">{{ course.students }}人学习</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 学习动态 -->
            <div class="activity-section">
                <div class="section-header">
                    <h3>学习动态</h3>
                </div>
                <van-list class="activity-list">
                    <van-cell
                        v-for="activity in activities"
                        :key="activity.id"
                        :title="activity.title"
                        :label="activity.time"
                        :value="activity.type"
                        class="activity-item"
                    >
                        <template #icon>
                            <van-image round width="40" height="40" :src="activity.avatar" />
                        </template>
                    </van-cell>
                </van-list>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'HomePage',
    data() {
        return {
            searchValue: '',
            banners: [
                {
                    id: 1,
                    title: '新课程上线',
                    subtitle: '人体解剖学基础课程全新发布',
                    image: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    link: '/courses'
                },
                {
                    id: 2,
                    title: '免费学习周',
                    subtitle: '精选课程限时免费开放',
                    image: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    link: '/courses'
                },
                {
                    id: 3,
                    title: '专业认证',
                    subtitle: '完成学习获得权威认证证书',
                    image: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    link: '/certificates'
                }
            ],
            quickEntries: [
                { id: 1, icon: 'play-circle-o', text: '课程学习', path: '/courses' },
                { id: 2, icon: 'certificate', text: '考试认证', path: '/exams' },
                { id: 3, icon: 'records', text: '学习记录', path: '/study' },
                { id: 4, icon: 'question-o', text: '在线答疑', path: '/qa' }
            ],
            categories: [
                {
                    id: 1,
                    name: '基础医学',
                    icon: 'medical-o',
                    courseCount: 45,
                    description: '人体解剖、生理、病理等基础学科'
                },
                {
                    id: 2,
                    name: '临床医学',
                    icon: 'hospital-o',
                    courseCount: 68,
                    description: '内科、外科、妇产科等临床学科'
                },
                {
                    id: 3,
                    name: '护理学',
                    icon: 'user-circle-o',
                    courseCount: 32,
                    description: '基础护理、专科护理技能培训'
                },
                {
                    id: 4,
                    name: '药学',
                    icon: 'bag-o',
                    courseCount: 28,
                    description: '药理学、药剂学、临床药学'
                }
            ],
            recommendCourses: [
                {
                    id: 1,
                    title: '人体解剖学基础',
                    teacher: '张教授',
                    cover: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    price: 0,
                    students: 1234,
                    isNew: true,
                    isHot: false,
                    isFree: true
                },
                {
                    id: 2,
                    title: '内科学精讲',
                    teacher: '李主任',
                    cover: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    price: 199,
                    students: 856,
                    isNew: false,
                    isHot: true,
                    isFree: false
                },
                {
                    id: 3,
                    title: '护理学基础',
                    teacher: '王护士长',
                    cover: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    price: 0,
                    students: 2341,
                    isNew: false,
                    isHot: false,
                    isFree: true
                },
                {
                    id: 4,
                    title: '药理学概论',
                    teacher: '赵博士',
                    cover: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    price: 299,
                    students: 567,
                    isNew: true,
                    isHot: true,
                    isFree: false
                }
            ],
            activities: [
                {
                    id: 1,
                    title: '张医生完成了《人体解剖学基础》',
                    time: '2小时前',
                    type: '课程完成',
                    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
                },
                {
                    id: 2,
                    title: '李护士获得了护理学认证证书',
                    time: '5小时前',
                    type: '证书获得',
                    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
                },
                {
                    id: 3,
                    title: '王医生开始学习《内科学精讲》',
                    time: '1天前',
                    type: '开始学习',
                    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
                }
            ]
        }
    },
    methods: {
        onSearch(value) {
            console.log('搜索:', value)
            // 跳转到搜索结果页
            this.$router.push(`/search?q=${encodeURIComponent(value)}`)
        },
        onClear() {
            this.searchValue = ''
        },
        goToBanner(banner) {
            console.log('点击轮播图:', banner.title)
            this.$router.push(banner.link)
        },
        goToEntry(entry) {
            console.log('点击快捷入口:', entry.text)
            this.$router.push(entry.path)
        },
        goToAllCategories() {
            console.log('查看全部分类')
            this.$router.push('/categories')
        },
        goToCategory(category) {
            console.log('点击分类:', category.name)
            this.$router.push(`/courses?category=${category.id}`)
        },
        goToAllCourses() {
            console.log('查看更多课程')
            this.$router.push('/courses')
        },
        goToCourse(course) {
            console.log('点击课程:', course.title)
            this.$router.push(`/course-detail/${course.id}`)
        },
        getPriceText(price) {
            return price === 0 ? '免费' : `¥${price}`
        }
    }
}
</script>

<style lang="scss" scoped>
.home-page {
    background-color: #f7f8fa;
    min-height: 100vh;

    .content {
        padding: 16px;
        // 搜索栏样式
        .search-section {
            padding: 16px;

            ::v-deep .van-search {
                background: transparent;

                .van-search__content {
                    background-color: rgba(255, 255, 255, 0.9);
                    border-radius: 20px;
                    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
                }
            }
        }

        // 轮播图样式
        .banner-section {
            margin-bottom: 16px;

            .banner-swipe {
                height: 180px;
                border-radius: 12px;
                overflow: hidden;
                margin: 0 16px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

                .banner-item {
                    position: relative;
                    height: 100%;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }

                    .banner-overlay {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
                        padding: 20px;
                        color: white;

                        .banner-title {
                            font-size: 18px;
                            font-weight: bold;
                            margin-bottom: 4px;
                        }

                        .banner-subtitle {
                            font-size: 14px;
                            opacity: 0.9;
                        }
                    }
                }
            }
        }

        // 快捷入口样式
        .quick-entry {
            background-color: #fff;
            margin: 0 16px 16px;
            border-radius: 12px;
            padding: 20px 0;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

            .entry-grid {
                ::v-deep .van-grid-item__content {
                    padding: 16px 8px;
                }
            }

            .entry-content {
                display: flex;
                flex-direction: column;
                align-items: center;

                .entry-icon {
                    font-size: 28px;
                    color: #1989fa;
                    margin-bottom: 8px;
                }

                .entry-text {
                    font-size: 12px;
                    color: #323233;
                    font-weight: 500;
                }
            }
        }

        // 分类菜单样式
        .category-menu {
            margin: 0 16px 16px;

            .category-grid {
                background-color: #fff;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

                .category-item {
                    ::v-deep .van-grid-item__content {
                        padding: 20px 16px;
                        height: 100px;
                    }

                    .category-content {
                        display: flex;
                        align-items: center;
                        height: 100%;

                        .category-icon {
                            width: 50px;
                            height: 50px;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            border-radius: 12px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-right: 16px;

                            .van-icon {
                                font-size: 24px;
                                color: white;
                            }
                        }

                        .category-info {
                            flex: 1;

                            .category-name {
                                font-size: 16px;
                                font-weight: bold;
                                color: #323233;
                                margin-bottom: 4px;
                            }

                            .category-count {
                                font-size: 12px;
                                color: #969799;
                            }
                        }
                    }
                }
            }
        }

        // 推荐课程样式
        .recommend-section {
            margin: 0 16px 16px;

            .course-list {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;

                .course-card {
                    background-color: #fff;
                    border-radius: 12px;
                    overflow: hidden;
                    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
                    transition: transform 0.2s ease;

                    &:active {
                        transform: scale(0.98);
                    }

                    .course-image {
                        position: relative;
                        height: 100px;

                        ::v-deep .van-image {
                            width: 100%;
                            height: 100%;
                        }

                        .course-tags {
                            position: absolute;
                            top: 8px;
                            left: 8px;
                            display: flex;
                            gap: 4px;
                        }
                    }

                    .course-info {
                        padding: 12px;

                        .course-title {
                            font-size: 14px;
                            font-weight: bold;
                            color: #323233;
                            margin-bottom: 4px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }

                        .course-teacher {
                            font-size: 12px;
                            color: #969799;
                            margin-bottom: 8px;
                        }

                        .course-meta {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;

                            .course-price {
                                font-size: 14px;
                                font-weight: bold;
                                color: #ee0a24;
                            }

                            .course-students {
                                font-size: 11px;
                                color: #969799;
                            }
                        }
                    }
                }
            }
        }

        // 学习动态样式
        .activity-section {
            margin: 0 16px 16px;

            .activity-list {
                background-color: #fff;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

                .activity-item {
                    ::v-deep .van-cell {
                        padding: 16px;

                        &:not(:last-child) {
                            border-bottom: 1px solid #f7f8fa;
                        }
                    }
                }
            }
        }

        // 通用区块标题样式
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 0 4px;

            h3 {
                font-size: 18px;
                font-weight: bold;
                color: #323233;
                margin: 0;
            }

            .more-btn {
                font-size: 14px;
                color: #1989fa;
                cursor: pointer;
            }
        }
    }
}
</style>
