<template>
    <div class="study-page">
        <van-nav-bar title="我的学习" fixed placeholder />

        <div class="content">
            <!-- 学习统计 -->
            <div class="study-stats">
                <van-grid :column-num="3" :border="false">
                    <van-grid-item>
                        <div class="stat-item">
                            <div class="stat-number">{{ studyStats.totalCourses }}</div>
                            <div class="stat-label">已学课程</div>
                        </div>
                    </van-grid-item>
                    <van-grid-item>
                        <div class="stat-item">
                            <div class="stat-number">{{ studyStats.totalHours }}</div>
                            <div class="stat-label">学习时长(小时)</div>
                        </div>
                    </van-grid-item>
                    <van-grid-item>
                        <div class="stat-item">
                            <div class="stat-number">{{ studyStats.certificates }}</div>
                            <div class="stat-label">获得证书</div>
                        </div>
                    </van-grid-item>
                </van-grid>
            </div>

            <!-- 学习进度 -->
            <div class="progress-section">
                <van-cell-group>
                    <van-cell
                        title="本周学习目标"
                        :value="weeklyGoal.current + '/' + weeklyGoal.target + '小时'"
                    >
                        <template #right-icon>
                            <van-progress
                                :percentage="weeklyGoal.percentage"
                                stroke-width="6"
                                color="#1989fa"
                            />
                        </template>
                    </van-cell>
                </van-cell-group>
            </div>

            <!-- 最近学习 -->
            <div class="recent-study">
                <van-divider content-position="left">最近学习</van-divider>
                <van-list>
                    <div
                        v-for="item in recentStudy"
                        :key="item.id"
                        class="study-item"
                        @click="continueLearning(item)"
                    >
                        <van-card
                            :title="item.courseTitle"
                            :desc="'上次学习: ' + item.lastStudyTime"
                            :thumb="item.cover"
                        >
                            <template #num>
                                <van-progress
                                    :percentage="item.progress"
                                    stroke-width="4"
                                    color="#1989fa"
                                />
                                <span class="progress-text">{{ item.progress }}%</span>
                            </template>
                            <template #footer>
                                <van-button size="mini" type="primary">继续学习</van-button>
                            </template>
                        </van-card>
                    </div>
                </van-list>
            </div>

            <!-- 学习计划 -->
            <div class="study-plan">
                <van-divider content-position="left">学习计划</van-divider>
                <van-cell-group>
                    <van-cell
                        v-for="plan in studyPlans"
                        :key="plan.id"
                        :title="plan.title"
                        :label="plan.description"
                        :value="plan.deadline"
                        is-link
                        @click="viewPlan(plan)"
                    />
                </van-cell-group>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'StudyPage',
    data() {
        return {
            studyStats: {
                totalCourses: 12,
                totalHours: 48,
                certificates: 3
            },
            weeklyGoal: {
                current: 8,
                target: 10,
                percentage: 80
            },
            recentStudy: [
                {
                    id: 1,
                    courseTitle: '人体解剖学基础',
                    lastStudyTime: '2小时前',
                    progress: 75,
                    cover: 'https://img.yzcdn.cn/vant/cat.jpeg'
                },
                {
                    id: 2,
                    courseTitle: '内科学精讲',
                    lastStudyTime: '昨天',
                    progress: 45,
                    cover: 'https://img.yzcdn.cn/vant/cat.jpeg'
                }
            ],
            studyPlans: [
                {
                    id: 1,
                    title: '基础医学认证',
                    description: '完成基础医学相关课程学习',
                    deadline: '2024-03-15'
                },
                {
                    id: 2,
                    title: '临床技能提升',
                    description: '掌握常见临床操作技能',
                    deadline: '2024-04-20'
                }
            ]
        }
    },
    methods: {
        continueLearning(item) {
            console.log('继续学习:', item.courseTitle)
            // 跳转到课程学习页面
            this.$router.push(`/course-study/${item.id}`)
        },
        viewPlan(plan) {
            console.log('查看计划:', plan.title)
            // 跳转到学习计划详情
            this.$router.push(`/study-plan/${plan.id}`)
        }
    }
}
</script>

<style lang="scss" scoped>
.study-page {
    background-color: #f7f8fa;
    min-height: 100vh;

    .content {
        padding: 16px;

        .study-stats {
            background-color: #fff;
            border-radius: 8px;
            margin-bottom: 16px;
            padding: 16px 0;

            .stat-item {
                text-align: center;

                .stat-number {
                    font-size: 24px;
                    font-weight: bold;
                    color: #1989fa;
                    margin-bottom: 4px;
                }

                .stat-label {
                    font-size: 12px;
                    color: #969799;
                }
            }
        }

        .progress-section {
            margin-bottom: 16px;

            ::v-deep .van-cell {
                background-color: #fff;
                border-radius: 8px;
            }
        }

        .recent-study,
        .study-plan {
            margin-bottom: 16px;

            .study-item {
                margin-bottom: 12px;

                ::v-deep .van-card {
                    background-color: #fff;
                    border-radius: 8px;
                    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
                }

                .progress-text {
                    font-size: 12px;
                    color: #969799;
                    margin-left: 8px;
                }
            }

            ::v-deep .van-cell-group {
                background-color: #fff;
                border-radius: 8px;
            }
        }
    }
}
</style>
